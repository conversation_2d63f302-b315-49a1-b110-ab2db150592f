package com.trinasolar.integration.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.trinasolar.integration.entity.BaseEntity;
import lombok.Builder;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;

/**
 * @className: InteAppSystemChange
 * @Description: 应用系统变更记录
 * @author: pengshy
 * @date: 2025/9/2 14:31
 */

@Builder
@Data
@TableName("tasp_base.inte_app_system_change")
public class InteAppSystemChange extends BaseEntity {

    /**
     * 应用系统ID
     */
    private String systemId;

    /**
     * 应用系统编码
     */
    @TableField(value = "simple_en_name", jdbcType = JdbcType.VARCHAR)
    private String simpleEnName;

    /**
     * 变更类型(1:新增,2:更新,3:删除)
     */
    private Integer changeType;

    /**
     * 变更后版本号 （数字自增）
     */
    private Integer currentVersion;

    /**
     * 变更后数据(JSON)
     */
    private String currentData;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;

    /**
     * 是否已处理(0:否,1:是)
     */
    private Integer isProcessed;
}
