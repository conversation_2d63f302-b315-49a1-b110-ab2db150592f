spring:
  application:
    name: kepler-integration
#    version: 1.0.0
#    profiles:
#      active: ${ACTIVE_ENV:default}
#  config:
#    import: optional:nacos:${spring.application.name}.yaml
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:***********}:${NACOS_PORT:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:LUOcheng@384}
        namespace: ${NACOS_NS:dev}
        register-enabled: true
        heart-beat-timeout: 15000
        heart-beat-interval: 5000
        ip-delete-timeout: 30000
      config:
        contextPath: /nacos
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        file-extension: yaml
        shared-configs:
          - application.yml
        namespace: ${NACOS_NS:dev}

# 定时任务配置
schedule:
  datashare:
    # 应用系统增量数据共享定时任务 - 每天晚上11点执行
    system-increment-cron: "0 2 9 * * ?"
    # 应用程序增量数据共享定时任务 - 每天晚上11点执行
    program-increment-cron: "0 3 9 * * ?"
    # 应用系统全量数据共享定时任务 - 每周六晚上11点执行
    system-full-cron: "0 59 8 * * ?"
    # 应用程序全量数据共享定时任务 - 每周六晚上11点执行
    program-full-cron: "0 59 8 * * ?"
